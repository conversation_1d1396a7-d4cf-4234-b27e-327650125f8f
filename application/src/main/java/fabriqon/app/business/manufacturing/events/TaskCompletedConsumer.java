package fabriqon.app.business.manufacturing.events;

import fabriqon.app.business.goods.GoodsService;
import fabriqon.app.business.goods.MaterialGood;
import fabriqon.app.business.inventory.InventoryEntry;
import fabriqon.app.business.inventory.InventoryService;
import fabriqon.app.business.manufacturing.ManufacturingOrder;
import fabriqon.app.business.manufacturing.ManufacturingService;
import fabriqon.app.business.manufacturing.ManufacturingTask;
import fabriqon.app.business.sales.SalesOrder;
import fabriqon.events.EventConsumer;
import fabriqon.events.Events;
import fabriqon.misc.Json;
import org.jooq.DSLContext;
import org.jooq.JSONB;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Stream;

import static fabriqon.app.business.manufacturing.ManufacturingOrder.UNRANKED_ORDER_VALUE;
import static fabriqon.jooq.classes.Tables.*;
import static fabriqon.jooq.classes.tables.MaterialGood.MATERIAL_GOOD;
import static java.util.Optional.ofNullable;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;

@Component
public class TaskCompletedConsumer extends EventConsumer<TaskCompleted> {

    private final DSLContext db;
    private final InventoryService inventoryService;
    private final GoodsService productsService;
    private final Events events;
    private final ManufacturingService manufacturingService;

    @Autowired
    public TaskCompletedConsumer(DSLContext db, InventoryService inventoryService, GoodsService productsService,
                                 Events events, ManufacturingService manufacturingService) {
        this.db = db;
        this.inventoryService = inventoryService;
        this.productsService = productsService;
        this.events = events;
        this.manufacturingService = manufacturingService;
    }

//    @Transactional //TODO: resolve issues with persistentClass (uncomment and run tests)
    @Override
    public void process(TaskCompleted event) {
        var ownerId = event.data.ownerId();
        var orderId = event.data.orderId();
        var order = load(ownerId, orderId);
        var isOrderDone = allTasksDone(ownerId, orderId) && (order.serviceId() != null  || isAllManufactured(event.data.quantity(), order));
        var newStatus = isOrderDone
                ? orderHasMaterials(order) ? ManufacturingOrder.Status.MANUFACTURED : ManufacturingOrder.Status.DONE
                : ManufacturingOrder.Status.MANUFACTURING;
        var orderUpdate = db.update(MANUFACTURING_ORDER)
                .set(MANUFACTURING_ORDER.STATUS, newStatus.name());
        if (isOrderDone) {
            orderUpdate = orderUpdate.set(MANUFACTURING_ORDER.MANUFACTURED_PRODUCTS,
                    JSONB.valueOf(Json.write(Stream.concat(
                                    ofNullable(order.manufacturedProducts()).orElse(List.of()).stream(),
                                    Stream.of(new ManufacturingOrder.ManufacturedProduct(event.data.quantity(), event.date)))
                            .toList())));
        }
        //in case the order is DONE we change the ranking to UNRANKED_ORDER_VALUE
        orderUpdate
                .set(MANUFACTURING_ORDER.RANKING, newStatus == ManufacturingOrder.Status.MANUFACTURED ? UNRANKED_ORDER_VALUE : order.ranking())
                .where(MANUFACTURING_ORDER.ID.eq(orderId))
                .execute();
        if (isOrderDone) {
            if (order.productId() != null) {
                var defaultInventoryUnitId = inventoryService.defaultInventoryUnitId(ownerId);
                var definition = db.selectFrom(MATERIAL_GOOD).where(MATERIAL_GOOD.ID.eq(order.productId())).fetchSingle().into(MaterialGood.class);
                inventoryService.fill(new InventoryEntry(null, null, null, false,
                        ownerId, order.productId(),
                        null,//TODO figure out location
                        null,
                        null,
                        null,
                        order.id(),
                        null,
                        null,
                        defaultInventoryUnitId,
                        LocalDateTime.now(),
                        null,
                        productsService.estimatedMaterialCosts(ownerId, order.productId())//this will anyway be replaced when the material issue note is created
                                .add(productsService.laborCosts(ownerId, order.productId()))//todo we should (or should we?) replace this with the calculation considering actual time it took and the people who worked on the tasks
                                .amount(),
                        definition.sellPrice.currency(),
                        null, null,
                        order.quantity()
                ));
                if (ManufacturingOrder.Status.DONE == newStatus) {
                    manufacturingService.done(ownerId, orderId, LocalDate.now(), null, null, List.of());
                }
                events.publish(
                        new ProductsManufactured(new ProductsManufactured.Data(
                                ownerId, order.id(), order.parentId(), order.salesOrderId(), order.productId(), order.quantity(), Instant.now(), order.customProduct()
                        )));
            } else if (order.serviceId() != null) {
                var salesOrderItem = salesOrder(ownerId, order.salesOrderId()).items().stream()
                        .filter(item -> order.serviceId().equals(item.serviceId())).findFirst().orElseThrow();
                db.insertInto(EXECUTED_SERVICES)
                        .set(EXECUTED_SERVICES.OWNER_ID, ownerId)
                        .set(EXECUTED_SERVICES.SALES_ORDER_ID, order.salesOrderId())
                        .set(EXECUTED_SERVICES.SERVICING_ORDER_ID, orderId)
                        .set(EXECUTED_SERVICES.SERVICE_ID, order.serviceId())
                        .set(EXECUTED_SERVICES.QUANTITY, order.quantity())
                        .set(EXECUTED_SERVICES.SALE_AMOUNT, salesOrderItem.price().amount())
                        .set(EXECUTED_SERVICES.SERVICE_CURRENCY, salesOrderItem.price().currency())
                        .execute();
            }
        }
    }

    private boolean allTasksDone(UUID ownerId, UUID orderId) {
        return db.select(MANUFACTURING_TASK.STATUS)
                .from(MANUFACTURING_TASK)
                .where(MANUFACTURING_TASK.OWNER_ID.eq(ownerId), MANUFACTURING_TASK.MANUFACTURING_ORDER_ID.eq(orderId))
                .fetchInto(ManufacturingTask.Status.class)
                .stream().allMatch(status -> status.equals(ManufacturingTask.Status.DONE));
    }

    private boolean isAllManufactured(BigDecimal manufactured, ManufacturingOrder order) {
        BigDecimal alreadyManufactured = ofNullable(order.manufacturedProducts()).orElse(List.of()).stream()
                .map(ManufacturingOrder.ManufacturedProduct::quantity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        var comparison = manufactured.add(alreadyManufactured).compareTo(order.quantity());
        if (comparison > 0) {
            throw new RuntimeException("Too many items are being registered as manufactured for this order");
        }
        return comparison == 0;
    }

    private ManufacturingOrder load(UUID owner, UUID manufacturingOrder) {
        return db.selectFrom(MANUFACTURING_ORDER)
                .where(MANUFACTURING_ORDER.ID.eq(manufacturingOrder), MANUFACTURING_ORDER.OWNER_ID.eq(owner))
                .fetchSingleInto(ManufacturingOrder.class);
    }

    private SalesOrder salesOrder(UUID ownerId, UUID salesOrder) {
        return db.selectFrom(SALES_ORDER)
                .where(SALES_ORDER.OWNER_ID.eq(ownerId), SALES_ORDER.ID.eq(salesOrder))
                .fetchSingleInto(SalesOrder.class);
    }

    private boolean orderHasMaterials(ManufacturingOrder order) {
        return isNotEmpty(order.manufacturingOperations().stream().flatMap(op -> op.materials().stream()).toList());
    }

}

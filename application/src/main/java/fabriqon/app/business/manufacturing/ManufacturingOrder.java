package fabriqon.app.business.manufacturing;

import com.fasterxml.jackson.core.type.TypeReference;
import fabriqon.app.common.model.Money;
import fabriqon.misc.Json;

import java.beans.ConstructorProperties;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public record ManufacturingOrder
        (
                UUID id,
                Instant createTime,
                Instant updateTime,
                boolean deleted,

                UUID ownerId,
                UUID parentId,
                UUID salesOrderId,
                String number,
                LocalDateTime productionDeadline,
                UUID assignedTo,
                Status status,
                UUID productId,
                UUID serviceId,
                BigDecimal quantity,
                List<ManufacturedProduct> manufacturedProducts,
                Integer ranking,
                String notes,
                boolean customProduct,
                List<ManufacturingOperation> manufacturingOperations,
                ManufacturingCosts manufacturingCosts,
                String base64ClientSignature
        ) {

    @ConstructorProperties({"id", "create_time", "update_time", "deleted", "owner_id", "parent_id", "sales_order_id", "number",
            "production_deadline", "assigned_user_id", "status", "product_id",  "service_id", "quantity", "manufactured_products", "ranking", "notes",
            "custom_product", "manufacturing_operations", "manufacturing_costs", "base64_client_signature"})
    public ManufacturingOrder(UUID id, Instant createTime, Instant updateTime, boolean deleted, UUID ownerId, UUID parentId,
                              UUID salesOrderId, String number, LocalDateTime productionDeadline, UUID assignedTo, Status status,
                              UUID productId, UUID serviceId, BigDecimal quantity, String manufacturedProducts,
                              Integer ranking, String notes, boolean customProduct, String manufacturingOperations,
                              String manufacturingCosts, String base64ClientSignature) {
        this(id, createTime, updateTime, deleted, ownerId, parentId, salesOrderId, number, productionDeadline, assignedTo, status,
                productId, serviceId, quantity,
                Json.readNullSafe(manufacturedProducts, new TypeReference<List<ManufacturedProduct>>() {
                }),
                ranking, notes, customProduct,
                Json.readNullSafe(manufacturingOperations, new TypeReference<List<ManufacturingOperation>>() {
                }),
                Json.readNullSafe(manufacturingCosts, ManufacturingCosts.class),
                base64ClientSignature
        );
    }

    public ManufacturingOrder setManufacturingOperations(List<ManufacturingOperation> manufacturingOperations) {
        return new ManufacturingOrder(id, createTime, updateTime, deleted, ownerId, parentId, salesOrderId, number, productionDeadline, assignedTo, status,
                productId, serviceId, quantity, manufacturedProducts, ranking, notes, customProduct, manufacturingOperations, manufacturingCosts, base64ClientSignature);
    }

    public ManufacturingOrder setQuantity(BigDecimal quantity) {
        return new ManufacturingOrder(id, createTime, updateTime, deleted, ownerId, parentId, salesOrderId, number, productionDeadline, assignedTo, status,
                productId, serviceId, quantity, manufacturedProducts, ranking, notes, customProduct, manufacturingOperations, manufacturingCosts, base64ClientSignature);
    }

    public enum Status {
        CUSTOMIZATION_NEEDED,
        SUBMITTED,
        MANUFACTURING,
        MANUFACTURED,
        DONE
    }

    public record ManufacturedProduct(BigDecimal quantity, Instant date) {
    }

    public record ManufacturingCosts(
       Map<UUID, Money> employeeHourlyRates,
       Map<UUID, Money> workstationHourlyRates,
       Map<String, Money> operationCostPerHourRates,
       Money manufacturingOverheadPerEmployeeHour
    ) {
    }

    public static final int UNRANKED_ORDER_VALUE = -1;
}
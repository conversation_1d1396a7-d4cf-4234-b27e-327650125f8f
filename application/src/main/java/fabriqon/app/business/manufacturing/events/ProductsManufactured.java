package fabriqon.app.business.manufacturing.events;

import fabriqon.events.Event;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;

public class ProductsManufactured extends Event {

    public final Data data;

    public ProductsManufactured(Data data) {
        this.data = data;
    }

    public record Data(
            UUID ownerId,
            UUID orderId,
            UUID parentId,
            UUID salesOrderId,
            UUID productId,
            BigDecimal quantity,
            Instant date,
            boolean customProduct
    ) {
    }

}

package fabriqon.e2e.tests;

import fabriqon.app.business.goods.RequiredMaterial;
import fabriqon.e2e.IntegrationTest;
import fabriqon.e2e.data.TestValueConstants;
import fabriqon.e2e.dsl.Account;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

import static org.junit.Assert.assertEquals;

public class ServicingOrderTests extends IntegrationTest {


    @Test
    public void manufactureService() {
        Account account = setup
                .account()
                .employee().create().account()
                .category().withName(TestValueConstants.CATEGORIES.poll()).create().account()
                .supplier().withName(TestValueConstants.SUPPLIERS.poll()).create().account()
                .location().withName("HQ").create().account()
                .customer().create().account();

        //create raw material
        var rawMaterialId = account.good()
                .withName(TestValueConstants.MATERIALS.poll())
                .withCategory(account.category().categoryId)
                .withDescription("raw material")
                .createRawMaterial().goodId;
        account.inventory().withGood(rawMaterialId).withQuantity(BigDecimal.TEN).fill();
        inventoryForGoodEquals(account, rawMaterialId, null, 10);

        //create the product
        var productId = account.good()
                .withCategory(account.category().categoryId)
                .withMaterials(List.of(new RequiredMaterial(List.of(UUID.fromString(rawMaterialId)), BigDecimal.ONE, false, false, false, BigDecimal.ZERO, null, null)))
                .create().goodId;

        //create the service
        var serviceId = account.service().withName("service").create().serviceId;

        var productMo = account.manufacturing().orderManufacturing(productId, new BigDecimal(2)).manufacturingOrderId;
        var serviceMo = account.servicing().orderServicing(serviceId, new BigDecimal(1)).servicingOrderId;

        assertEquals(0, new BigDecimal(2).compareTo(account.manufacturing().order(productMo).manufacturingOperations().getFirst().materials().getFirst().reservedTotal()));
        assertEquals(0, new BigDecimal(2).compareTo(account.inventory().itemHistory(rawMaterialId).reservedStock()));

        account.servicing().goodsAccompanyingNote(serviceMo, rawMaterialId, new BigDecimal(3));

        assertEquals(0, new BigDecimal(2).compareTo(account.manufacturing().order(productMo).manufacturingOperations().getFirst().materials().getFirst().reservedTotal()));
        assertEquals(0, new BigDecimal(5).compareTo(account.inventory().itemHistory(rawMaterialId).reservedStock()));
        assertEquals(1, account.servicing().order(serviceMo).goodsAccompanyingNotes().size());

        //try to create another note with more than the available materials
        try {
            account.servicing().goodsAccompanyingNote(serviceMo, rawMaterialId, new BigDecimal(30));
        } catch (AssertionError e) {
            assertEquals("Status expected:<200> but was:<400>", e.getMessage());
        }

        //now reserve 6 more so the stock gets allocated to the service mo and the product mo has only 1 left
        account.servicing().goodsAccompanyingNote(serviceMo, rawMaterialId, new BigDecimal(6));

        assertEquals(0, new BigDecimal(1).compareTo(account.manufacturing().order(productMo).manufacturingOperations().getFirst().materials().getFirst().reservedTotal()));
        assertEquals(0, new BigDecimal(11).compareTo(account.inventory().itemHistory(rawMaterialId).reservedStock()));
        assertEquals(0, new BigDecimal(-1).compareTo(account.inventory().itemHistory(rawMaterialId).balance()));
        assertEquals(2, account.servicing().order(serviceMo).goodsAccompanyingNotes().size());
    }


    private void inventoryForGoodEquals(Account account, String materialGoodId, UUID inventoryUnitId, int quantity) {
        var stock = account.inventory().stockInformationFor(materialGoodId, inventoryUnitId);
        assertEquals(0, stock.quantity().compareTo(BigDecimal.valueOf(quantity)));
    }

}

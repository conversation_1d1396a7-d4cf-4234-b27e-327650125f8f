package fabriqon.e2e.tests;

import fabriqon.app.business.goods.RequiredMaterial;
import fabriqon.app.common.model.Address;
import fabriqon.app.http.controllers.GoodsAccompanyingNotesController;
import fabriqon.e2e.IntegrationTest;
import fabriqon.e2e.data.TestValueConstants;
import fabriqon.e2e.dsl.Account;
import fabriqon.misc.Json;
import org.junit.Test;
import org.springframework.http.MediaType;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.CoreMatchers.isA;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

public class SalesOrdersTests extends IntegrationTest {

    @Test
    public void createAndListAndDetailsForSalesOrder() throws Exception {

        Account account = setup
                .account()
                .category().withName(TestValueConstants.CATEGORIES.poll()).create().account()
                .supplier().withName(TestValueConstants.SUPPLIERS.poll()).create().account()
                .location().withName("HQ").create().account()
                .customer().create().account();

        //create raw material
        var rawMaterialId = account.good()
                .withName(TestValueConstants.MATERIALS.poll())
                .withCategory(account.category().categoryId)
                .withDescription("raw material")
                .createRawMaterial().goodId;
        //add raw materials to inventory
        account.inventory().withGood(rawMaterialId).withQuantity(BigDecimal.valueOf(100)).fill();

        //create the product
        var productName = TestValueConstants.PRODUCTS.poll();
        account.good()
                .withName(productName)
                .withCategory(account.category().categoryId)
                .withDescription("something to write with")
                .withSellPrice(321)
                .withMarkup(BigDecimal.valueOf(1.5))
                .withMaterials(List.of(new RequiredMaterial(List.of(UUID.fromString(rawMaterialId)), BigDecimal.TEN, false, false, false, BigDecimal.ZERO, null, null)))
                .create();

        //fill inventory for product
        account.inventory()
                .withGood(account.good().goodId)
                .withQuantity(BigDecimal.valueOf(2))
                .fill();

        //create the sale order
        account.sale().withQuantity(BigDecimal.valueOf(5)).withSellPrice(321).create();

        var orderList = account.sale().list();
        assertEquals(1, orderList.size());
        assertEquals(1, orderList.get(0).items().size());
        assertEquals(account.good().goodId, orderList.get(0).items().get(0).productId().toString());

        String orderId = orderList.get(0).id().toString();

        var order = account.sale().order(orderId);
        assertEquals(orderId, order.id().toString());
        assertEquals("SUBMITTED", order.status());
        assertEquals(account.customer().customerId, order.customer().id().toString());
        assertEquals(1909, order.totalAmount().amount());
        assertEquals(304, order.taxAmount().amount());
        assertEquals(account.good().goodId, order.items().get(0).productId().toString());
        assertEquals(0, order.items().get(0).reserved().compareTo(BigDecimal.valueOf(2.0)));

        //update the order
        mvc.perform(post("/sales/orders/" + orderId + "/update")
                        .content("{\"deliveryDeadline\" : \"2023-12-04T09:59:34.00Z\"}")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.deliveryDeadline", is("2023-12-04T09:59:34")));

        //order manufacturing for the missing products
        account.sale().manufactureMissing(account.good().goodId, BigDecimal.valueOf(3));

        order = account.sale().order(orderId);
        assertEquals("PROCESSING", order.status());
        assertEquals("billing & shipping", order.shippingAddress().name());
        assertEquals(1909, order.totalAmount().amount());
        assertEquals(account.good().goodId, order.items().get(0).productId().toString());
        assertEquals(0, order.items().get(0).reserved().compareTo(BigDecimal.valueOf(2.0)));
        assertEquals(0, order.items().get(0).manufacturingOrder().orderedQuantity().compareTo(BigDecimal.valueOf(3.0)));

        //manufacture the products
        account.manufacturing().withManufacturingOrderId(account.sale().manufacturingOrderId).completeTask();

        order = account.sale().order(orderId);
        assertEquals("PROCESSING", order.status());
        assertEquals(0, order.items().get(0).reserved().compareTo(BigDecimal.valueOf(5.0)));
        assertEquals(0, order.items().get(0).manufacturingOrder().orderedQuantity().compareTo(BigDecimal.valueOf(3.0)));
        assertEquals(0, order.items().get(0).manufacturingOrder().manufacturedQuantity().compareTo(BigDecimal.valueOf(3.0)));

        //invoice the order
        account.sale().invoice();
        mvc.perform(get("/invoices/" + account.sale().invoiceId + "/details")
                        .header("Authorization", "Bearer " + account.accountId)
                        .accept(MediaType.APPLICATION_JSON_VALUE))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.customer.id", is(account.customer().customerId)))
                .andExpect(jsonPath("$.dueDate", isA(String.class)))
                .andExpect(jsonPath("$.vat.amount", is(304)))
                .andExpect(jsonPath("$.items.[0].name", is(productName)))
                .andExpect(jsonPath("$.items.[0].unitPrice.amount", is(321)));

        //create a goods accompanying note for the order
        account.sale().goodsAccompanyingNote(LocalDate.now().plusDays(10),
                new Address("from", "RO", "Cluj Napoca", "Cluj",  null, null, null, null),
                new Address("to", "RO", "Bucuresti", "Ilfov",  null, null, null, null),
                account.employee().employeeId,
                "CJ01ABC", "Notes here",
                List.of(new GoodsAccompanyingNotesController.GoodsAccompanyingNoteDefinition.GoodsAccompanyingNoteDefinitionItem(UUID.fromString(account.good().goodId), BigDecimal.ONE)));

        mvc.perform(get("/goods-accompanying-notes/" + account.sale().goodsAccompanyingNoteId + "/details")
                        .header("Authorization", "Bearer " + account.accountId)
                        .accept(MediaType.APPLICATION_JSON_VALUE))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.customer.id", is(account.customer().customerId)))
                .andExpect(jsonPath("$.deliveryDate", isA(String.class)))
                .andExpect(jsonPath("$.items.[0].name", is(productName)))
                .andExpect(jsonPath("$.items.[0].price.amount", is(321)));
    }

    @Test
    public void rankingSupport() throws Exception {

        Account account = setup
                .account()
                .category().withName("Office Supplies").create().account()
                .supplier().withName("IKEA").create().account()
                .location().withName("HQ").create().account()
                .customer().create().account();

        //create raw material
        var rawMaterialId = account.good()
                .withName(TestValueConstants.MATERIALS.poll())
                .withCategory(account.category().categoryId)
                .withDescription("raw material")
                .create().goodId;
        //add raw materials to inventory
        account.inventory().withGood(rawMaterialId).withQuantity(BigDecimal.valueOf(100)).fill();

        //create the product
        var productName = TestValueConstants.PRODUCTS.poll();
        account.good()
                .withName(productName)
                .withCategory(account.category().categoryId)
                .withDescription("something to write with")
                .withSellPrice(321)
                .withMarkup(BigDecimal.valueOf(1.5))
                .withMaterials(List.of(new RequiredMaterial(List.of(UUID.fromString(rawMaterialId)), BigDecimal.TEN, false, false, false, BigDecimal.ZERO, null, null)))
                .create();

        //fill inventory for product
        account.inventory()
                .withGood(account.good().goodId)
                .withQuantity(BigDecimal.valueOf(10))
                .fill();

        var firstOrder = account.sale().withQuantity(BigDecimal.valueOf(8)).withSellPrice(321).create().salesOrderId;
        var secondOrder = account.sale().withQuantity(BigDecimal.valueOf(10)).withSellPrice(321).create().salesOrderId;

        mvc.perform(get("/sales/orders/list")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("[0].id", is(firstOrder)))
                .andExpect(jsonPath("[0].status", is("SUBMITTED")))
                .andExpect(jsonPath("[0].ranking", is(0)))
                .andExpect(jsonPath("[1].id", is(secondOrder)))
                .andExpect(jsonPath("[1].status", is("SUBMITTED")))
                .andExpect(jsonPath("[1].ranking", is(1)));

        mvc.perform(post("/sales/orders/ranking")
                        .header("Authorization", "Bearer " + account.accountId)
                        .content(Json.write(List.of(secondOrder, firstOrder)))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        mvc.perform(get("/sales/orders/list")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("[0].id", is(secondOrder)))
                .andExpect(jsonPath("[0].ranking", is(0)))
                .andExpect(jsonPath("[1].id", is(firstOrder)))
                .andExpect(jsonPath("[1].ranking", is(1)));
    }

    @Test
    public void servicesSupport() throws Exception {

        Account account = setup
                .account()
                .category().withName("Office Supplies").create().account()
                .supplier().withName("IKEA").create().account()
                .location().withName("HQ").create().account()
                .customer().create().account();

        var serviceId = account.service().withName("service 1").create().serviceId;

        //create the sale order
        account.sale()
                .withService(serviceId)
                .withQuantity(BigDecimal.valueOf(1))
                .withSellPrice(1200)
                .create();

        var order = account.sale().order(account.sale().salesOrderId);
        assertEquals(1, order.items().size());
        assertNull(order.items().get(0).productId());
        assertEquals("service 1", order.items().get(0).name());

        //'manufacture' service
        account.sale().manufactureMissing(null, UUID.fromString(serviceId), BigDecimal.valueOf(1));

        order = account.sale().order(account.sale().salesOrderId);
        assertEquals("PROCESSING", order.status());
        assertEquals(account.customer().customerId, order.customer().id().toString());
        assertEquals(1428, order.totalAmount().amount());
        assertEquals("service 1", order.items().get(0).name());
        assertEquals("NEED_SUPPLY", order.items().get(0).status());
        assertNull(order.items().get(0).reserved());

    }

}

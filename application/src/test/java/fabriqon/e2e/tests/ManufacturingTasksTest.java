package fabriqon.e2e.tests;

import fabriqon.app.business.goods.RequiredMaterial;
import fabriqon.e2e.IntegrationTest;
import fabriqon.e2e.data.TestValueConstants;
import fabriqon.e2e.dsl.Account;
import org.junit.Test;
import org.springframework.http.MediaType;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

import static java.util.UUID.fromString;
import static org.hamcrest.CoreMatchers.is;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

public class ManufacturingTasksTest extends IntegrationTest {

    @Test
    public void tasksAreCreatedAndScheduled() throws Exception {
        Account account = setup
                .account()
                .category().withName(TestValueConstants.CATEGORIES.poll()).create().account()
                .supplier().withName(TestValueConstants.SUPPLIERS.poll()).create().account()
                .location().withName("HQ").create().account()
                .customer().create().account();

        var joeyId = account.employee().withName("Joey Tribbiani").create().employeeId;
        var chandlerId = account.employee().withName("Chandler Bing").create().employeeId;

        var cuttingWorkstationId = account.manufacturingWorkstation().withName("Cutting machine").create().workstationId;
        var paintingWorkstationId = account.manufacturingWorkstation().withName("Painting machine").create().workstationId;

        var cuttingOperationId = account.manufacturingOperationTemplate()
                .withName("cutting")
                .withWorkstations(List.of(fromString(cuttingWorkstationId)))
                .withEmployees(List.of(joeyId))
                .withDuration(10)
                .create().operationTemplateId;

        var paintingOperationId = account.manufacturingOperationTemplate()
                .withName("painting")
                .withWorkstations(List.of(fromString(paintingWorkstationId)))
                .withEmployees(List.of(chandlerId))
                .withDuration(10)
                .create().operationTemplateId;

        //create raw material
        var rawMaterialId = account.good()
                .withName(TestValueConstants.MATERIALS.poll())
                .withCategory(account.category().categoryId)
                .withDescription("raw material")
                .createRawMaterial()
                .goodId;
        //add raw materials to inventory
        account.inventory().withGood(rawMaterialId).withQuantity(BigDecimal.TEN).fill();

        //create the product
        account.good()
                .withCategory(account.category().categoryId)
                .withMaterials(List.of(new RequiredMaterial(List.of(UUID.fromString(account.good().goodId)), BigDecimal.TEN, false, false, false, BigDecimal.ZERO, null, null)))
                .withOperationIds(List.of(cuttingOperationId, paintingOperationId))
                .create();

        account.manufacturing().orderManufacturing(account.good().goodId, BigDecimal.TEN);

        mvc.perform(get("/manufacturing/orders/" + account.manufacturing().manufacturingOrderId + "/details")
                        .header("Authorization", "Bearer " + account.accountId)
                        .accept(MediaType.APPLICATION_JSON)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.manufacturingTasks.[0].name", is("cutting")))
                .andExpect(jsonPath("$.manufacturingTasks.[0].assignedWorkstations.[0].id", is(cuttingWorkstationId)))
                .andExpect(jsonPath("$.manufacturingTasks.[0].assignedEmployees.[0].id", is(joeyId.toString())))
                .andExpect(jsonPath("$.manufacturingTasks.[1].name", is("painting")))
                .andExpect(jsonPath("$.manufacturingTasks.[1].assignedWorkstations.[0].id", is(paintingWorkstationId)))
                .andExpect(jsonPath("$.manufacturingTasks.[1].assignedEmployees.[0].id", is(chandlerId.toString())))
        ;
    }
}

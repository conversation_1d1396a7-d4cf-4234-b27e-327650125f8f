package fabriqon.e2e.tests;

import fabriqon.app.business.goods.RequiredMaterial;
import fabriqon.e2e.IntegrationTest;
import fabriqon.e2e.dsl.Account;
import fabriqon.misc.Tuple;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

import static org.junit.Assert.*;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

public class ManufacturingOrdersSubassemblyTests extends IntegrationTest {

    @Test
    public void manufactureProductWithSubassemblyAndCheckInventory() throws Exception {
        Account account = setup
                .account()
                .category().withName("category for product and subassemblies").create().account()
                .location().create().account()
                .supplier().create().account();

        var defaultInventoryUnitId = account.inventory().defaultInventoryUnit();

        //create the raw materials
        var rawMaterialId1 = account.good()
                .withName("material_1")
                .withCategory(account.category().categoryId)
                .create().goodId;
        account.inventory().withGood(rawMaterialId1).withQuantity(BigDecimal.TEN).fill();

        var rawMaterialId2 = account.good()
                .withName("material_2")
                .withCategory(account.category().categoryId)
                .create().goodId;
        account.inventory().withGood(rawMaterialId2).withQuantity(BigDecimal.TEN).fill();

        var subassemblyId = account.good()
                .withName("subassembly").withCategory(account.category().categoryId)
                .withMaterials(List.of(new RequiredMaterial(
                        List.of(UUID.fromString(rawMaterialId1), UUID.fromString(rawMaterialId2)),
                        BigDecimal.ONE, false,  true, false, BigDecimal.ZERO, null, null)))
                .create().goodId;

        var productId = account.good()
                .withName("product").withCategory(account.category().categoryId)
                .withMaterials(List.of(
                        new RequiredMaterial(List.of(UUID.fromString(rawMaterialId1)), BigDecimal.ONE, false,  false, false, BigDecimal.ZERO, null, null),
                        new RequiredMaterial(List.of(UUID.fromString(subassemblyId)), BigDecimal.ONE, false,  false, false, BigDecimal.ZERO, null, null)))
                .create().goodId;

        var productMOId = account.manufacturing().orderManufacturing(productId, BigDecimal.ONE).manufacturingOrderId;

        var allOrders = account.manufacturing().list();
        assertEquals("Expected 2 manufacturing orders (product + automatic subassembly)", 2, allOrders.size());

        var subassemblyMO = allOrders.stream()
                .filter(order -> order.product() != null && subassemblyId.equals(order.product().id().toString()))
                .findFirst()
                .orElse(null);
        assertNotNull("Manufacturing order should be automatically created for subassembly", subassemblyMO);
        assertEquals(productMOId, subassemblyMO.parentId().toString());
        assertEquals(1, subassemblyMO.manufacturingTasks().size());

        var productMO = account.manufacturing().order(productMOId);
        assertEquals(subassemblyId, productMO.manufacturingOperations().getFirst().materials().get(1).materialGoods().getFirst().id().toString());
        assertFalse(productMO.manufacturingOperations().getFirst().materials().get(1).allAvailable());

        var subassemblyMOId = subassemblyMO.id().toString();
        account.manufacturing().completeTask(subassemblyMOId);
        account.manufacturing().done(subassemblyMOId, List.of(Tuple.of(UUID.fromString(rawMaterialId1), BigDecimal.ONE)));
        inventoryForGoodEquals(account, productId, defaultInventoryUnitId, 0);
        inventoryForGoodEquals(account, subassemblyId, defaultInventoryUnitId, 1);
        inventoryForGoodEquals(account, rawMaterialId1, defaultInventoryUnitId, 9);

        productMO = account.manufacturing().order(productMOId);
        assertEquals(subassemblyId, productMO.manufacturingOperations().getFirst().materials().get(1).materialGoods().getFirst().id().toString());
        assertTrue(productMO.manufacturingOperations().getFirst().materials().get(1).allAvailable());

        account.manufacturing().completeTask(productMOId);
        account.manufacturing().done(productMOId, List.of(
                Tuple.of(UUID.fromString(rawMaterialId1), BigDecimal.ONE),
                Tuple.of(UUID.fromString(subassemblyId), BigDecimal.ONE)
        ));

        inventoryForGoodEquals(account, productId, defaultInventoryUnitId, 1);
        inventoryForGoodEquals(account, subassemblyId, defaultInventoryUnitId, 0);
        inventoryForGoodEquals(account, rawMaterialId1, defaultInventoryUnitId, 8);
    }

    @Test
    public void subassemblyMONumberBasedOnParent() {
        TestData testData = createAccountWithSubassembly();

        var productMOId = testData.account.manufacturing().orderManufacturing(testData.productId, BigDecimal.ONE).manufacturingOrderId;
        var allOrders = testData.account.manufacturing().list();

        var parentMO = testData.account.manufacturing().order(productMOId);
        var subassemblyMO = allOrders.stream()
                .filter(order -> order.parentId() != null && order.parentId().toString().equals(productMOId))
                .findFirst()
                .orElse(null);

        assertNotNull("Subassembly MO should be created", subassemblyMO);
        assertTrue("Subassembly MO number should be based on parent",
                subassemblyMO.number().startsWith(parentMO.number() + " / "));
    }

    @Test
    public void subassemblyMOCompletedReservesStockForParent() {
        Account account = createAccountWithSubassembly();

        var productMOId = account.manufacturing().orderManufacturing(account.good().goodId, BigDecimal.ONE).manufacturingOrderId;
        var subassemblyMO = account.manufacturing().getChildOrders(productMOId).get(0);

        // Complete subassembly MO
        account.manufacturing().completeTask(subassemblyMO.id().toString());
        account.manufacturing().done(subassemblyMO.id().toString(),
                List.of(Tuple.of(UUID.fromString(account.good().rawMaterialId1), BigDecimal.ONE)));

        // Check that parent MO now has materials available
        var parentMO = account.manufacturing().order(productMOId);
        var subassemblyMaterial = parentMO.manufacturingOperations().getFirst().materials().stream()
                .filter(m -> m.materialGoods().getFirst().id().toString().equals(account.good().subassemblyId))
                .findFirst()
                .orElse(null);

        assertNotNull("Subassembly material should exist", subassemblyMaterial);
        assertTrue("Subassembly material should be available after completion", subassemblyMaterial.allAvailable());
    }

    @Test
    public void rankingChangeReReservesStockAndDeletesUnnecessarySubassemblyMOs() {
        Account account = createAccountWithSubassembly();

        // Create two product MOs
        var productMO1Id = account.manufacturing().orderManufacturing(account.good().goodId, BigDecimal.ONE).manufacturingOrderId;
        var productMO2Id = account.manufacturing().orderManufacturing(account.good().goodId, BigDecimal.ONE).manufacturingOrderId;

        // Add enough subassembly stock for one product
        account.inventory().withGood(account.good().subassemblyId).withQuantity(BigDecimal.ONE).fill();

        var initialChildOrders = account.manufacturing().getChildOrders(productMO1Id).size() +
                                account.manufacturing().getChildOrders(productMO2Id).size();

        // Change ranking - put second order first
        account.manufacturing().applyRanking(List.of(UUID.fromString(productMO2Id), UUID.fromString(productMO1Id)));

        var finalChildOrders = account.manufacturing().getChildOrders(productMO1Id).size() +
                              account.manufacturing().getChildOrders(productMO2Id).size();

        // Should have fewer child orders as stock was re-reserved
        assertTrue("Should have fewer subassembly MOs after ranking change", finalChildOrders < initialChildOrders);
    }

    @Test
    public void updateOrderReReservesStockAndDeletesUnnecessarySubassemblyMOs() {
        Account account = createAccountWithSubassembly();

        var productMOId = account.manufacturing().orderManufacturing(account.good().goodId, BigDecimal.valueOf(2)).manufacturingOrderId;
        var initialChildOrders = account.manufacturing().getChildOrders(productMOId).size();

        // Add enough subassembly stock
        account.inventory().withGood(account.good().subassemblyId).withQuantity(BigDecimal.valueOf(2)).fill();

        // Update order quantity to 1
        account.manufacturing().updateOrder(productMOId, BigDecimal.ONE);

        var finalChildOrders = account.manufacturing().getChildOrders(productMOId).size();

        // Should have fewer child orders as less quantity is needed
        assertTrue("Should have fewer subassembly MOs after update", finalChildOrders <= initialChildOrders);
    }

    @Test
    public void deleteOrderDeletesSubassemblyMOs() {
        Account account = createAccountWithSubassembly();

        var productMOId = account.manufacturing().orderManufacturing(account.good().goodId, BigDecimal.ONE).manufacturingOrderId;
        var childOrders = account.manufacturing().getChildOrders(productMOId);

        assertFalse("Should have child orders initially", childOrders.isEmpty());

        // Delete parent order
        account.manufacturing().deleteOrder(productMOId);

        // Check that child orders are also deleted
        for (var childOrder : childOrders) {
            assertFalse("Child order should be deleted",
                    account.manufacturing().orderExists(childOrder.id().toString()));
        }
    }

    private Account createAccountWithSubassembly() {
        Account account = setup
                .account()
                .category().withName("category for product and subassemblies").create().account()
                .location().create().account()
                .supplier().create().account();

        // Create raw materials
        var rawMaterialId1 = account.good()
                .withName("material_1")
                .withCategory(account.category().categoryId)
                .create().goodId;
        account.inventory().withGood(rawMaterialId1).withQuantity(BigDecimal.TEN).fill();

        var rawMaterialId2 = account.good()
                .withName("material_2")
                .withCategory(account.category().categoryId)
                .create().goodId;
        account.inventory().withGood(rawMaterialId2).withQuantity(BigDecimal.TEN).fill();

        // Create subassembly
        var subassemblyId = account.good()
                .withName("subassembly").withCategory(account.category().categoryId)
                .withMaterials(List.of(new RequiredMaterial(
                        List.of(UUID.fromString(rawMaterialId1), UUID.fromString(rawMaterialId2)),
                        BigDecimal.ONE, false, true, false, BigDecimal.ZERO, null, null)))
                .create().goodId;

        // Create product that uses subassembly
        account.good()
                .withName("product").withCategory(account.category().categoryId)
                .withMaterials(List.of(
                        new RequiredMaterial(List.of(UUID.fromString(rawMaterialId1)), BigDecimal.ONE, false, false, false, BigDecimal.ZERO, null, null),
                        new RequiredMaterial(List.of(UUID.fromString(subassemblyId)), BigDecimal.ONE, false, false, false, BigDecimal.ZERO, null, null)))
                .create();

        // Store IDs for easy access in tests
        account.good().rawMaterialId1 = rawMaterialId1;
        account.good().subassemblyId = subassemblyId;

        return account;
    }

    private void inventoryForGoodEquals(Account account, String materialGoodId, UUID inventoryUnitId, int quantity) {
        var stock = account.inventory().stockInformationFor(materialGoodId, inventoryUnitId);
        assertEquals(0, stock.quantity().compareTo(BigDecimal.valueOf(quantity)));
    }

}

package fabriqon.e2e.dsl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.jayway.jsonpath.JsonPath;
import fabriqon.app.business.manufacturing.ManufacturingTask;
import fabriqon.app.business.manufacturing.UsedMaterial;
import fabriqon.app.business.sales.GoodsAccompanyingNote;
import fabriqon.app.http.controllers.GoodsAccompanyingNotesController;
import fabriqon.app.http.controllers.SalesOrdersController;
import fabriqon.app.http.controllers.manufacturing.ManufacturingOrdersController;
import fabriqon.e2e.SetupUtils;
import fabriqon.misc.Json;
import fabriqon.misc.Tuple;
import lombok.SneakyThrows;
import org.springframework.http.MediaType;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class Manufacturing {
    private final Account account;

    public String manufacturingOrderId;


    public Manufacturing(Account account) {
        this.account = account;
    }


    public Manufacturing withManufacturingOrderId(String manufacturingOrderId) {
        this.manufacturingOrderId = manufacturingOrderId;
        return this;
    }

    @SneakyThrows
    public Manufacturing orderManufacturing(String productId, BigDecimal quantity) {
        var result = account.dsl().mvc()
                .perform(post("/manufacturing/orders/create")
                        .content(Json.write(new ManufacturingOrdersController.CreateManufacturingOrder(
                                LocalDateTime.now(),
                                UUID.fromString(productId), null,
                                quantity, null, false)))
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
        manufacturingOrderId = JsonPath.read(result.getResponse().getContentAsString(), "$.id");
        return this;
    }

    @SneakyThrows
    public Manufacturing deleteOrder(String manufacturingOrderId) {
        account.dsl().mvc().perform(delete("/manufacturing/orders/" + manufacturingOrderId + "/delete")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
        return this;
    }

    @SneakyThrows
    public Manufacturing updateOrder(String manufacturingOrderId, BigDecimal quantity) {
        account.dsl().mvc().perform(post("/manufacturing/orders/" + manufacturingOrderId + "/update")
                        .content(Json.write(new ManufacturingOrdersController.UpdateManufacturingOrder(
                                null, null, quantity, null, null)))
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
        return this;
    }

    @SneakyThrows
    public Manufacturing applyRanking(List<UUID> orderIds) {
        account.dsl().mvc().perform(post("/manufacturing/orders/ranking")
                        .content(Json.write(orderIds))
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
        return this;
    }

    /**
     * gets the first task that is in TO DO and completes it
     */
    @SneakyThrows
    public Manufacturing completeTask() {
        return completeTask(manufacturingOrderId);
    }

    /**
     * gets the first task that is in TO DO and completes it
     */
    @SneakyThrows
    public Manufacturing completeTask(String manufacturingOrderId) {
        var orderResult = account.dsl().mvc()
                .perform(get("/manufacturing/orders/" + manufacturingOrderId + "/details")
                        .header("Authorization", "Bearer " + account.accountId)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
        List<Map<String, Object>> tasks = JsonPath.read(orderResult.getResponse().getContentAsString(), "$.manufacturingTasks");
        var firstTodo = tasks.stream()
                .filter(task -> ManufacturingTask.Status.TODO.name().equals(task.get("status"))).findFirst()
                .orElseThrow(() -> new RuntimeException("no tasks with status TODO found"));

        account.dsl().mvc()
                .perform(post("/manufacturing/tasks/" + firstTodo.get("id") + "/done")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
        return this;
    }


    @SneakyThrows
    public Manufacturing done(List<Tuple.Tuple2<UUID, BigDecimal>> materials) {
        return done(manufacturingOrderId, materials);
    }
    @SneakyThrows
    public Manufacturing done(String manufacturingOrderId, List<Tuple.Tuple2<UUID, BigDecimal>> materials) {
        var orderResult = account.dsl().mvc()
                .perform(post("/manufacturing/orders/" + manufacturingOrderId + "/done")
                        .content(Json.write(new ManufacturingOrdersController.ManufacturingOrderDone(LocalDate.now(), "manager", "worker",
                                materials.stream().map(m -> new UsedMaterial(m.a(), m.b())).toList())))
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
        return this;
    }

    @SneakyThrows
    public List<ManufacturingOrdersController.ManufacturingOrderDetails> list() {
        var result = account.dsl().mvc()
                .perform(get("/manufacturing/orders/list")
                        .accept(MediaType.APPLICATION_JSON)
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
        return Json.read(result.getResponse().getContentAsString(), new TypeReference<List<ManufacturingOrdersController.ManufacturingOrderDetails>>() {});
    }

    @SneakyThrows
    public ManufacturingOrdersController.ManufacturingOrderDetails order(String orderId) {
        var result = account.dsl().mvc()
                .perform(get("/manufacturing/orders/" + orderId + "/details")
                        .accept(MediaType.APPLICATION_JSON)
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
        return Json.read(result.getResponse().getContentAsString(), ManufacturingOrdersController.ManufacturingOrderDetails.class);
    }

    public List<ManufacturingOrdersController.ManufacturingOrderDetails> getChildOrders(String parentOrderId) {
        return list().stream()
                .filter(order -> order.parentId() != null && order.parentId().toString().equals(parentOrderId))
                .toList();
    }

    public boolean orderExists(String orderId) {
        return list().stream()
                .anyMatch(order -> order.id().toString().equals(orderId));
    }

    public Account account() {
        return account;
    }
}

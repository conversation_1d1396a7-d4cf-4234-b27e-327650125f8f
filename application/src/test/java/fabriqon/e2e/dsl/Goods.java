package fabriqon.e2e.dsl;

import com.jayway.jsonpath.JsonPath;
import fabriqon.app.business.goods.MaterialGood;
import fabriqon.app.business.goods.RequiredMaterial;
import fabriqon.app.business.manufacturing.ManufacturingOperation;
import fabriqon.app.common.model.MeasurementUnit;
import fabriqon.app.common.model.Money;
import fabriqon.app.http.controllers.goods.GoodsController;
import fabriqon.misc.Json;
import lombok.SneakyThrows;
import org.apache.commons.lang3.RandomUtils;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.ResultActions;

import java.math.BigDecimal;
import java.util.Currency;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import static fabriqon.e2e.data.Utils.mediumRandomInt;
import static fabriqon.e2e.data.Utils.smallRandomInt;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

public class Goods {
    private final Account account;
    public String goodId;
    public String rawMaterialId1;
    public String subassemblyId;
    private Data data;


    public Goods(Account account) {
        this.account = account;
        this.data = new Data();
    }

    public Goods withName(String name) {
        this.data.name = name;
        return this;
    }

    public Goods withCategory(String categoryId) {
        this.data.categoryId = categoryId;
        return this;
    }

    public Goods withDescription(String description) {
        this.data.description = description;
        return this;
    }

    public Goods withSellPrice(long sellPrice) {
        this.data.sellPrice = sellPrice;
        return this;
    }

    public Goods withMarkup(BigDecimal markup) {
        this.data.markup = markup;
        return this;
    }

    public Goods withMaterials(List<RequiredMaterial> materials) {
        this.data.materials = materials;
        return this;
    }

    public Goods withOperations(List<ManufacturingOperation> manufacturingOperations) {
        this.data.operations = manufacturingOperations;
        return this;
    }

    public Goods withOperationIds(List<String> operationIds) {
        this.data.operationIds = operationIds;
        return this;
    }

    @SneakyThrows
    public Goods create() {
        var result = createWithResult(true);
        result.andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("id", notNullValue(String.class)))
                .andExpect(jsonPath("name", is(data.name)));
        goodId = JsonPath.read(result.andReturn().getResponse().getContentAsString(), "$.id");
        return this;
    }

    @SneakyThrows
    public Goods createRawMaterial() {
        var result = createWithResult(false);
        result.andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("id", notNullValue(String.class)))
                .andExpect(jsonPath("name", is(data.name)));
        goodId = JsonPath.read(result.andReturn().getResponse().getContentAsString(), "$.id");
        return this;
    }

    @SneakyThrows
    public ResultActions createWithResult(boolean product) {
        var materials = data.materials;
        if (product && data.materials == null) {
            materials = List.of(new RequiredMaterial(createDefaultMaterial(), BigDecimal.TEN, false, false, false, BigDecimal.ZERO, null, null));
        }
        var finalMaterials = materials;
        return account.dsl().mvc()
                .perform(post("/goods/create")
                        .content(Json.write(new GoodsController.GoodDefinition(
                                data.name, "C-" + mediumRandomInt(), null, UUID.fromString(data.categoryId),
                                data.description,
                                product ? new Money(data.sellPrice, Currency.getInstance("RON")) : null,
                                product ? data.markup : null, product ? new BigDecimal("0.19") : null, data.criticalOnHand, MeasurementUnit.PIECE,
                                product ? !data.operationIds.isEmpty()
                                        ? data.operationIds.stream()
                                        .map(id -> new ManufacturingOperation(UUID.fromString(id), null, null, null, smallRandomInt(),
                                                null, null, null, null, null, finalMaterials))
                                        .toList()
                                        : data.operations != null ? data.operations : List.of(createDefaultOperation(finalMaterials))
                                : null,
                                product,
                                BigDecimal.ONE,
                                null, null
                        )))
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON));
    }


    @SneakyThrows
    public GoodsController.GoodDetails details(String productId) {
        var result = account.dsl().mvc()
                .perform(get("/goods/" + productId + "/details")
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andReturn();
        return Json.read(result.getResponse().getContentAsString(), GoodsController.GoodDetails.class);
    }


    @SneakyThrows
    private ManufacturingOperation createDefaultOperation(List<RequiredMaterial> finalMaterials) {
        var workstationId = account.manufacturingWorkstation().withName("W - " + smallRandomInt()).create().workstationId;
        var employeeId = account.employee().withName("E - " + smallRandomInt()).create().employeeId;
        return new ManufacturingOperation(
                null,
                List.of(UUID.fromString(workstationId)), List.of(employeeId),
                "cut - " + smallRandomInt(), 100, false, null, null, null, null, finalMaterials);
    }

    @SneakyThrows
    public List<UUID> createDefaultMaterial() {
        var result = account.dsl().mvc()
                .perform(post("/goods/create")
                        .content(Json.write(new GoodsController.GoodDefinition(
                                "defaultMaterial " + smallRandomInt(), "M-" + smallRandomInt(), null, UUID.fromString(data.categoryId),
                                "default material",
                                new Money(smallRandomInt(), Currency.getInstance("RON")),
                                BigDecimal.valueOf(.1), new BigDecimal("0.19"), BigDecimal.TEN, MeasurementUnit.PIECE,
                                null, false, null, null, null
                        )))
                        .header("Authorization", "Bearer " + account.accountId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andReturn();
        var materialId = UUID.fromString(JsonPath.read(result.getResponse().getContentAsString(), "$.id"));
        account.inventory().withGood(materialId.toString()).fill();
        return List.of(materialId);
    }

    public Account account() {
        return account;
    }

    public List<MaterialGood.VariantOption> variantOptions = List.of(
            new MaterialGood.VariantOption("Color", Set.of("Blue", "Red")),
            new MaterialGood.VariantOption("Size", Set.of("Small", "Medium", "Large"))
            );

    private static class Data {
        String name = "good " + smallRandomInt();
        String categoryId;
        String description = "some random description";
        long sellPrice = mediumRandomInt();
        BigDecimal markup = BigDecimal.valueOf(RandomUtils.nextInt(1, 10));
        BigDecimal criticalOnHand = BigDecimal.valueOf(RandomUtils.nextInt(1, 100));
        List<RequiredMaterial> materials = null;
        List<ManufacturingOperation> operations = null;
        List<String> operationIds = List.of();
    }
}
